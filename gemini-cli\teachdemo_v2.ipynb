# 导入所需的模块
import os
import sys
import time
import json
import random
import asyncio
from datetime import datetime
from collections import namedtuple, deque, Counter, defaultdict
from functools import partial, lru_cache, wraps
from contextlib import contextmanager

print("=" * 60)
print("🐍 Python完整学习指南 - 从零基础到高手 🐍")
print("=" * 60)

# 数字类型
age = 25                    # 整数 (int)
height = 175.5             # 浮点数 (float)
complex_num = 3 + 4j       # 复数 (complex)

print(f"整数: {age}, 类型: {type(age)}")
print(f"浮点数: {height}, 类型: {type(height)}")
print(f"复数: {complex_num}, 类型: {type(complex_num)}")

# 字符串类型
name = "张三"               # 字符串
greeting = '你好，世界！'    # 单引号也可以
multiline = """这是一个
多行字符串
示例"""

print(f"字符串: {name}")
print(f"字符串长度: {len(name)}")
print(f"多行字符串:\n{multiline}")

# 布尔类型
is_student = True
is_working = False
print(f"布尔值: {is_student}, {is_working}")

text = "Python编程很有趣"
print(f"原字符串: {text}")
print(f"切片 [0:6]: {text[0:6]}")
print(f"切片 [6:]: {text[6:]}")
print(f"反向切片 [::-1]: {text[::-1]}")

# 字符串格式化的三种方式
name = "小明"
score = 95.5

# 方式1: % 格式化 (旧式)
old_style = "学生 %s 的成绩是 %.1f 分" % (name, score)
print(f"旧式格式化: {old_style}")

# 方式2: .format() 方法
new_style = "学生 {} 的成绩是 {:.1f} 分".format(name, score)
print(f"新式格式化: {new_style}")

# 方式3: f-string (推荐，Python 3.6+)
f_string = f"学生 {name} 的成绩是 {score:.1f} 分"
print(f"f-string: {f_string}")

a, b = 10, 3
print(f"a = {a}, b = {b}")
print(f"加法: {a} + {b} = {a + b}")
print(f"减法: {a} - {b} = {a - b}")
print(f"乘法: {a} * {b} = {a * b}")
print(f"除法: {a} / {b} = {a / b}")
print(f"整除: {a} // {b} = {a // b}")
print(f"取余: {a} % {b} = {a % b}")
print(f"幂运算: {a} ** {b} = {a ** b}")

# 比较运算符
print(f"比较运算: {a} > {b} = {a > b}")
print(f"比较运算: {a} == {b} = {a == b}")

# 逻辑运算符
x, y = True, False
print(f"逻辑与: {x} and {y} = {x and y}")
print(f"逻辑或: {x} or {y} = {x or y}")
print(f"逻辑非: not {x} = {not x}")

# 创建列表
fruits = ["苹果", "香蕉", "橙子"]
numbers = [1, 2, 3, 4, 5]
mixed = ["文本", 42, True, 3.14]

print(f"水果列表: {fruits}")
print(f"数字列表: {numbers}")
print(f"混合列表: {mixed}")

# 列表操作
fruits.append("葡萄")           # 添加元素
fruits.insert(1, "草莓")        # 在指定位置插入
print(f"添加后: {fruits}")

removed = fruits.pop()          # 删除并返回最后一个元素
print(f"删除 '{removed}' 后: {fruits}")

# 列表推导式 (List Comprehension)
squares = [x**2 for x in range(1, 6)]
even_squares = [x**2 for x in range(1, 11) if x % 2 == 0]
print(f"平方数: {squares}")
print(f"偶数的平方: {even_squares}")

coordinates = (10, 20)
person_info = ("张三", 25, "工程师")
single_tuple = (42,)  # 注意逗号

print(f"坐标: {coordinates}")
print(f"个人信息: {person_info}")
print(f"解包: 姓名={person_info[0]}, 年龄={person_info[1]}, 职业={person_info[2]}")

# 元组解包
x, y = coordinates
name, age, job = person_info
print(f"解包后: x={x}, y={y}")

# 创建字典
student = {
    "姓名": "李四",
    "年龄": 20,
    "专业": "计算机科学",
    "成绩": [85, 92, 78, 96]
}

print(f"学生信息: {student}")
print(f"姓名: {student['姓名']}")
print(f"年龄: {student.get('年龄', '未知')}")

# 字典操作
student["学号"] = "2023001"     # 添加新键值对
student["年龄"] = 21            # 修改值
print(f"更新后: {student}")

# 遍历字典
print("遍历字典:")
for key, value in student.items():
    print(f"  {key}: {value}")

# 字典推导式
word_lengths = {word: len(word) for word in ["python", "java", "javascript"]}
print(f"单词长度: {word_lengths}")

# 创建集合
colors = {"红", "绿", "蓝", "红"}  # 重复的"红"会被自动去除
numbers_set = set([1, 2, 3, 2, 1])

print(f"颜色集合: {colors}")
print(f"数字集合: {numbers_set}")

# 集合操作
set1 = {1, 2, 3, 4}
set2 = {3, 4, 5, 6}

print(f"集合1: {set1}")
print(f"集合2: {set2}")
print(f"并集: {set1 | set2}")
print(f"交集: {set1 & set2}")
print(f"差集: {set1 - set2}")

def check_grade(score):
    """根据分数判断等级"""
    if score >= 90:
        return "优秀"
    elif score >= 80:
        return "良好"
    elif score >= 70:
        return "中等"
    elif score >= 60:
        return "及格"
    else:
        return "不及格"

test_scores = [95, 85, 75, 65, 55]
for score in test_scores:
    grade = check_grade(score)
    print(f"分数 {score}: {grade}")

# for 循环
print("for 循环示例:")
for i in range(5):
    print(f"  计数: {i}")

# 遍历列表
fruits = ["苹果", "香蕉", "橙子"]
for index, fruit in enumerate(fruits):
    print(f"  {index}: {fruit}")

# while 循环
print("\nwhile 循环示例:")
count = 0
while count < 3:
    print(f"  while 计数: {count}")
    count += 1

# 循环控制
print("\n循环控制 (break 和 continue):")
for i in range(10):
    if i == 3:
        continue  # 跳过当前迭代
    if i == 7:
        break     # 跳出循环
    print(f"  数字: {i}")

def greet(name, greeting="你好"):
    """
    问候函数
    
    参数:
        name (str): 姓名
        greeting (str): 问候语，默认为"你好"
    
    返回:
        str: 完整的问候语
    """
    return f"{greeting}, {name}!"

# 函数调用
print(greet("小明"))
print(greet("小红", "早上好"))

def flexible_function(required, default="默认值", *args, **kwargs):
    """演示不同类型的参数"""
    print(f"必需参数: {required}")
    print(f"默认参数: {default}")
    print(f"可变位置参数 (*args): {args}")
    print(f"可变关键字参数 (**kwargs): {kwargs}")

flexible_function("必需的", "自定义默认值", 1, 2, 3, key1="值1", key2="值2")

def apply_operation(func, numbers):
    """将函数应用到数字列表"""
    return [func(x) for x in numbers]

def square(x):
    return x ** 2

def cube(x):
    return x ** 3

numbers = [1, 2, 3, 4, 5]
print(f"原数字: {numbers}")
print(f"平方: {apply_operation(square, numbers)}")
print(f"立方: {apply_operation(cube, numbers)}")

# lambda 函数
double = lambda x: x * 2
print(f"使用 lambda 翻倍: {apply_operation(double, numbers)}")

# 内置高阶函数
print(f"map 函数: {list(map(lambda x: x * 2, numbers))}")
print(f"filter 函数: {list(filter(lambda x: x % 2 == 0, numbers))}")

def create_multiplier(factor):
    """创建一个乘法器函数"""
    def multiplier(number):
        return number * factor
    return multiplier

double_func = create_multiplier(2)
triple_func = create_multiplier(3)

print(f"使用闭包: double_func(5) = {double_func(5)}")
print(f"使用闭包: triple_func(5) = {triple_func(5)}")

class Student:
    """学生类示例"""
    
    # 类变量 (所有实例共享)
    school_name = "Python学院"
    student_count = 0
    
    def __init__(self, name, age, student_id):
        """构造函数 - 初始化实例"""
        self.name = name        # 实例变量
        self.age = age
        self.student_id = student_id
        self.grades = []
        Student.student_count += 1  # 每创建一个学生，计数加1
    
    def add_grade(self, subject, score):
        """添加成绩"""
        self.grades.append({"科目": subject, "分数": score})
        print(f"{self.name} 的 {subject} 成绩 {score} 分已添加")
    
    def get_average_grade(self):
        """计算平均成绩"""
        if not self.grades:
            return 0
        total = sum(grade["分数"] for grade in self.grades)
        return total / len(self.grades)
    
    def introduce(self):
        """自我介绍"""
        avg_grade = self.get_average_grade()
        return f"我是 {self.name}，学号 {self.student_id}，平均成绩 {avg_grade:.1f} 分"
    
    @classmethod
    def get_student_count(cls):
        """类方法 - 获取学生总数"""
        return cls.student_count
    
    @staticmethod
    def is_passing_grade(score):
        """静态方法 - 判断是否及格"""
        return score >= 60
    
    def __str__(self):
        """字符串表示 (用户友好)"""
        return f"学生: {self.name} (ID: {self.student_id})"
    
    def __repr__(self):
        """字符串表示 (开发者友好)"""
        return f"Student('{self.name}', {self.age}, '{self.student_id}')"

# 创建学生对象
student1 = Student("张三", 20, "S001")
student2 = Student("李四", 19, "S002")

print(f"创建了 {Student.get_student_count()} 个学生")
print(f"学校名称: {Student.school_name}")

# 使用对象方法
student1.add_grade("数学", 85)
student1.add_grade("英语", 92)
student2.add_grade("数学", 78)

print(student1.introduce())
print(student2.introduce())
print(f"85分是否及格: {Student.is_passing_grade(85)}")

class GraduateStudent(Student):
    """研究生类 - 继承自学生类"""
    
    def __init__(self, name, age, student_id, research_area):
        super().__init__(name, age, student_id)  # 调用父类构造函数
        self.research_area = research_area
        self.thesis_progress = 0
    
    def update_thesis_progress(self, progress):
        """更新论文进度"""
        self.thesis_progress = min(100, max(0, progress))
        print(f"{self.name} 的论文进度更新为 {self.thesis_progress}%")
    
    def introduce(self):
        """重写父类方法"""
        base_intro = super().introduce()
        return f"{base_intro}，研究方向: {self.research_area}"
    
    def __str__(self):
        return f"研究生: {self.name} (研究方向: {self.research_area})"

# 创建研究生对象
grad_student = GraduateStudent("王五", 24, "G001", "人工智能")
grad_student.add_grade("高级算法", 95)
grad_student.update_thesis_progress(60)

print(grad_student.introduce())
print(f"研究生总数: {Student.get_student_count()}")

class BankAccount:
    """银行账户类 - 演示属性装饰器"""
    
    def __init__(self, account_number, initial_balance=0):
        self.account_number = account_number
        self._balance = initial_balance  # 私有属性约定
    
    @property
    def balance(self):
        """余额属性的 getter"""
        return self._balance
    
    @balance.setter
    def balance(self, amount):
        """余额属性的 setter"""
        if amount < 0:
            raise ValueError("余额不能为负数")
        self._balance = amount
    
    def deposit(self, amount):
        """存款"""
        if amount > 0:
            self._balance += amount
            print(f"存款 {amount} 元，当前余额: {self._balance} 元")
        else:
            print("存款金额必须大于0")
    
    def withdraw(self, amount):
        """取款"""
        if amount > self._balance:
            print("余额不足")
        elif amount > 0:
            self._balance -= amount
            print(f"取款 {amount} 元，当前余额: {self._balance} 元")
        else:
            print("取款金额必须大于0")

# 使用银行账户
account = BankAccount("*********", 1000)
print(f"账户余额: {account.balance} 元")

account.deposit(500)
account.withdraw(200)

# 尝试设置负余额 (会抛出异常，但我们用 try-except 处理)
try:
    account.balance = -100
except ValueError as e:
    print(f"错误: {e}")

def safe_divide(a, b):
    """安全除法函数"""
    try:
        result = a / b
        print(f"{a} ÷ {b} = {result}")
    except ZeroDivisionError:
        print("错误: 不能除以零!")
        return None
    except TypeError:
        print("错误: 参数类型不正确!")
        return None
    except Exception as e:
        print(f"未知错误: {e}")
        return None
    else:
        print("除法运算成功完成")
        return result
    finally:
        print("除法函数执行完毕")

# 测试异常处理
safe_divide(10, 2)
safe_divide(10, 0)
safe_divide("10", 2)

class CustomError(Exception):
    """自定义异常类"""
    def __init__(self, message, error_code=None):
        super().__init__(message)
        self.error_code = error_code

class AgeValidationError(CustomError):
    """年龄验证异常"""
    pass

def validate_age(age):
    """验证年龄"""
    if not isinstance(age, int):
        raise AgeValidationError("年龄必须是整数", "TYPE_ERROR")
    if age < 0:
        raise AgeValidationError("年龄不能为负数", "NEGATIVE_AGE")
    if age > 150:
        raise AgeValidationError("年龄不能超过150岁", "TOO_OLD")
    print(f"年龄 {age} 验证通过")

# 测试自定义异常
test_ages = [25, -5, 200, "abc"]
for age in test_ages:
    try:
        validate_age(age)
    except AgeValidationError as e:
        print(f"年龄验证失败: {e} (错误代码: {e.error_code})")

# 写入文件
filename = "demo_data.txt"
data_to_write = ["第一行数据", "第二行数据", "第三行数据"]

try:
    with open(filename, 'w', encoding='utf-8') as file:
        for line in data_to_write:
            file.write(line + '\n')
    print(f"数据已写入文件 {filename}")
    
    # 读取文件
    with open(filename, 'r', encoding='utf-8') as file:
        content = file.read()
        print(f"文件内容:\n{content}")
    
    # 按行读取
    with open(filename, 'r', encoding='utf-8') as file:
        lines = file.readlines()
        print("按行读取:")
        for i, line in enumerate(lines, 1):
            print(f"  第{i}行: {line.strip()}")

except FileNotFoundError:
    print(f"文件 {filename} 不存在")
except PermissionError:
    print(f"没有权限访问文件 {filename}")
finally:
    # 清理文件
    if os.path.exists(filename):
        os.remove(filename)
        print(f"临时文件 {filename} 已删除")

# 创建示例数据
student_data = {
    "学生信息": [
        {"姓名": "张三", "年龄": 20, "成绩": [85, 92, 78]},
        {"姓名": "李四", "年龄": 19, "成绩": [90, 88, 95]},
        {"姓名": "王五", "年龄": 21, "成绩": [87, 91, 89]}
    ],
    "创建时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
}

json_filename = "students.json"

try:
    # 写入 JSON 文件
    with open(json_filename, 'w', encoding='utf-8') as file:
        json.dump(student_data, file, ensure_ascii=False, indent=2)
    print(f"JSON 数据已写入 {json_filename}")
    
    # 读取 JSON 文件
    with open(json_filename, 'r', encoding='utf-8') as file:
        loaded_data = json.load(file)
    
    print("从 JSON 文件读取的数据:")
    print(f"创建时间: {loaded_data['创建时间']}")
    for student in loaded_data['学生信息']:
        avg_score = sum(student['成绩']) / len(student['成绩'])
        print(f"  {student['姓名']} (年龄: {student['年龄']}, 平均分: {avg_score:.1f})")

finally:
    # 清理文件
    if os.path.exists(json_filename):
        os.remove(json_filename)
        print(f"临时文件 {json_filename} 已删除")

def timing_decorator(func):
    """计时装饰器 - 测量函数执行时间"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.perf_counter()
        result = func(*args, **kwargs)
        end_time = time.perf_counter()
        print(f"函数 '{func.__name__}' 执行耗时: {end_time - start_time:.6f} 秒")
        return result
    return wrapper

def retry_decorator(max_attempts=3):
    """重试装饰器 - 失败时自动重试"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            for attempt in range(max_attempts):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    if attempt == max_attempts - 1:
                        print(f"函数 '{func.__name__}' 在 {max_attempts} 次尝试后仍然失败")
                        raise e
                    print(f"第 {attempt + 1} 次尝试失败: {e}，正在重试...")
        return wrapper
    return decorator

@timing_decorator
def slow_calculation(n):
    """模拟耗时计算"""
    total = 0
    for i in range(n):
        total += i ** 2
    return total

@retry_decorator(max_attempts=3)
def unreliable_function():
    """模拟不稳定的函数"""
    if random.random() < 0.7:  # 70% 概率失败
        raise Exception("随机失败")
    return "成功执行"

# 测试装饰器
result = slow_calculation(100000)
print(f"计算结果: {result}")

try:
    result = unreliable_function()
    print(f"不稳定函数结果: {result}")
except Exception as e:
    print(f"最终失败: {e}")

def fibonacci_generator(n):
    """斐波那契数列生成器"""
    a, b = 0, 1
    count = 0
    while count < n:
        yield a
        a, b = b, a + b
        count += 1

# 使用生成器
print("斐波那契数列前10项:")
fib_gen = fibonacci_generator(10)
for num in fib_gen:
    print(num, end=" ")
print()

# 生成器表达式
squares_gen = (x**2 for x in range(1, 6))
print(f"平方数生成器: {list(squares_gen)}")

# namedtuple - 命名元组
Point = namedtuple('Point', ['x', 'y'])
p1 = Point(10, 20)
p2 = Point(30, 40)
print(f"点1: {p1}, 点2: {p2}")
print(f"点1的x坐标: {p1.x}, y坐标: {p1.y}")

# deque - 双端队列
task_queue = deque(['任务1', '任务2', '任务3'])
task_queue.appendleft('紧急任务')  # 在左端添加
task_queue.append('普通任务')      # 在右端添加
print(f"任务队列: {task_queue}")

completed_task = task_queue.popleft()  # 从左端取出
print(f"完成任务: {completed_task}, 剩余: {task_queue}")

# Counter - 计数器
text = "hello world python programming"
char_count = Counter(text)
word_count = Counter(text.split())

print(f"字符计数: {char_count}")
print(f"单词计数: {word_count}")
print(f"最常见的3个字符: {char_count.most_common(3)}")

# defaultdict - 默认字典
student_grades = defaultdict(list)
student_grades['张三'].append(85)
student_grades['张三'].append(92)
student_grades['李四'].append(78)

print(f"学生成绩: {dict(student_grades)}")
print(f"王五的成绩: {student_grades['王五']}")  # 自动创建空列表

# partial - 偏函数
def power(base, exponent):
    return base ** exponent

square = partial(power, exponent=2)
cube = partial(power, exponent=3)

print(f"5的平方: {square(5)}")
print(f"5的立方: {cube(5)}")

# lru_cache - 缓存装饰器
@lru_cache(maxsize=128)
def expensive_fibonacci(n):
    """带缓存的斐波那契函数"""
    if n < 2:
        return n
    return expensive_fibonacci(n-1) + expensive_fibonacci(n-2)

# 测试缓存效果
start_time = time.perf_counter()
result = expensive_fibonacci(30)
end_time = time.perf_counter()
print(f"斐波那契(30) = {result}, 耗时: {end_time - start_time:.6f} 秒")

# 第二次调用会很快，因为有缓存
start_time = time.perf_counter()
result = expensive_fibonacci(30)
end_time = time.perf_counter()
print(f"第二次调用耗时: {end_time - start_time:.6f} 秒 (使用缓存)")

class DatabaseConnection:
    """模拟数据库连接的上下文管理器"""
    
    def __init__(self, db_name):
        self.db_name = db_name
        self.connection = None
    
    def __enter__(self):
        print(f"连接到数据库: {self.db_name}")
        self.connection = f"连接到{self.db_name}"
        return self.connection
    
    def __exit__(self, exc_type, exc_value, exc_traceback):
        print(f"关闭数据库连接: {self.db_name}")
        if exc_type:
            print(f"发生异常: {exc_type.__name__}: {exc_value}")
        return False  # 不抑制异常

# 使用上下文管理器
with DatabaseConnection("用户数据库") as conn:
    print(f"使用连接: {conn}")
    print("执行数据库操作...")

# 使用 contextmanager 装饰器创建上下文管理器
@contextmanager
def timer_context(name):
    """计时上下文管理器"""
    print(f"开始计时: {name}")
    start_time = time.perf_counter()
    try:
        yield start_time
    finally:
        end_time = time.perf_counter()
        print(f"结束计时: {name}, 耗时: {end_time - start_time:.6f} 秒")

# 使用计时上下文管理器
with timer_context("复杂计算"):
    # 模拟一些计算
    result = sum(i**2 for i in range(10000))
    print(f"计算结果: {result}")

async def fetch_data(source, delay):
    """模拟异步获取数据"""
    print(f"开始从 {source} 获取数据...")
    await asyncio.sleep(delay)  # 模拟I/O等待
    result = f"来自 {source} 的数据"
    print(f"从 {source} 获取数据完成")
    return result

async def process_data(data):
    """模拟异步处理数据"""
    print(f"开始处理数据: {data}")
    await asyncio.sleep(0.5)  # 模拟处理时间
    processed = f"已处理的 {data}"
    print(f"数据处理完成: {processed}")
    return processed

async def async_main():
    """异步主函数"""
    print("启动异步任务...")
    start_time = time.perf_counter()
    
    # 并发执行多个异步任务
    tasks = [
        fetch_data("API服务器", 1.0),
        fetch_data("数据库", 1.5),
        fetch_data("文件系统", 0.8)
    ]
    
    # 等待所有任务完成
    results = await asyncio.gather(*tasks)
    
    # 处理获取到的数据
    processed_results = []
    for data in results:
        processed = await process_data(data)
        processed_results.append(processed)
    
    end_time = time.perf_counter()
    print(f"\n所有异步任务完成，总耗时: {end_time - start_time:.2f} 秒")
    print("处理结果:")
    for result in processed_results:
        print(f"  - {result}")

# 运行异步代码
# 注意：在 Jupyter Notebook 中，可以直接使用 await
# 如果在普通 Python 脚本中，需要使用 asyncio.run(async_main())
await async_main()

class AsyncNumberGenerator:
    """异步数字生成器"""
    
    def __init__(self, start, end):
        self.start = start
        self.end = end
        self.current = start
    
    def __aiter__(self):
        return self
    
    async def __anext__(self):
        if self.current < self.end:
            await asyncio.sleep(0.1)  # 模拟异步操作
            value = self.current
            self.current += 1
            return value
        else:
            raise StopAsyncIteration

# 异步迭代演示
print("异步迭代器演示:")
async for num in AsyncNumberGenerator(1, 6):
    print(f"  异步生成的数字: {num}")

# 查看模块信息
print(f"当前模块名: {__name__}")
print(f"Python版本: {sys.version}")
print(f"Python路径: {sys.path[:3]}...")  # 只显示前3个路径

print("\n导入方式示例:")
print("1. import module_name")
print("2. from module_name import function_name")
print("3. from module_name import function_name as alias")
print("4. import module_name as alias")

class MathUtils:
    """数学工具类"""
    
    @staticmethod
    def factorial(n):
        """计算阶乘"""
        if n < 0:
            raise ValueError("阶乘不能计算负数")
        if n <= 1:
            return 1
        return n * MathUtils.factorial(n - 1)
    
    @staticmethod
    def is_prime(n):
        """判断是否为质数"""
        if n < 2:
            return False
        for i in range(2, int(n ** 0.5) + 1):
            if n % i == 0:
                return False
        return True
    
    @staticmethod
    def gcd(a, b):
        """计算最大公约数"""
        while b:
            a, b = b, a % b
        return a

# 使用自定义模块
print(f"5的阶乘: {MathUtils.factorial(5)}")
print(f"17是质数吗: {MathUtils.is_prime(17)}")
print(f"48和18的最大公约数: {MathUtils.gcd(48, 18)}")

class StudentManagementSystem:
    """学生管理系统"""
    
    def __init__(self):
        self.students = {}
        self.next_id = 1
    
    def add_student(self, name, age, major):
        """添加学生"""
        student_id = f"S{self.next_id:03d}"
        self.students[student_id] = {
            "姓名": name,
            "年龄": age,
            "专业": major,
            "成绩": {}
        }
        self.next_id += 1
        print(f"学生 {name} (ID: {student_id}) 已添加")
        return student_id
    
    def add_grade(self, student_id, subject, score):
        """添加成绩"""
        if student_id in self.students:
            self.students[student_id]["成绩"][subject] = score
            print(f"已为学生 {student_id} 添加 {subject} 成绩: {score}")
        else:
            print(f"学生 {student_id} 不存在")
    
    def get_student_average(self, student_id):
        """计算学生平均成绩"""
        if student_id not in self.students:
            return None
        
        grades = self.students[student_id]["成绩"]
        if not grades:
            return 0
        
        return sum(grades.values()) / len(grades)
    
    def list_students(self):
        """列出所有学生"""
        print("\n学生列表:")
        print("-" * 50)
        for student_id, info in self.students.items():
            avg_grade = self.get_student_average(student_id)
            print(f"{student_id}: {info['姓名']} | 年龄: {info['年龄']} | "
                  f"专业: {info['专业']} | 平均分: {avg_grade:.1f}")
    
    def export_to_json(self, filename):
        """导出到JSON文件"""
        try:
            with open(filename, 'w', encoding='utf-8') as file:
                json.dump(self.students, file, ensure_ascii=False, indent=2)
            print(f"学生数据已导出到 {filename}")
        except Exception as e:
            print(f"导出失败: {e}")

# 使用学生管理系统
sms = StudentManagementSystem()

# 添加学生
id1 = sms.add_student("张三", 20, "计算机科学")
id2 = sms.add_student("李四", 19, "数学")
id3 = sms.add_student("王五", 21, "物理")

# 添加成绩
sms.add_grade(id1, "数学", 85)
sms.add_grade(id1, "英语", 92)
sms.add_grade(id1, "编程", 96)

sms.add_grade(id2, "数学", 98)
sms.add_grade(id2, "英语", 87)

sms.add_grade(id3, "物理", 91)
sms.add_grade(id3, "数学", 88)

# 显示学生列表
sms.list_students()

def performance_tips_demo():
    """性能优化技巧演示"""
    
    # 技巧1: 使用列表推导式而不是循环
    numbers = range(1000)
    
    # 较慢的方式
    start_time = time.perf_counter()
    squares_slow = []
    for num in numbers:
        squares_slow.append(num ** 2)
    slow_time = time.perf_counter() - start_time
    
    # 较快的方式
    start_time = time.perf_counter()
    squares_fast = [num ** 2 for num in numbers]
    fast_time = time.perf_counter() - start_time
    
    print(f"循环方式耗时: {slow_time:.6f} 秒")
    print(f"列表推导式耗时: {fast_time:.6f} 秒")
    print(f"性能提升: {slow_time / fast_time:.2f} 倍")
    
    # 技巧2: 使用集合进行快速查找
    large_list = list(range(10000))
    large_set = set(large_list)
    
    search_value = 9999
    
    # 在列表中查找 (较慢)
    start_time = time.perf_counter()
    found_in_list = search_value in large_list
    list_time = time.perf_counter() - start_time
    
    # 在集合中查找 (较快)
    start_time = time.perf_counter()
    found_in_set = search_value in large_set
    set_time = time.perf_counter() - start_time
    
    print(f"\n在列表中查找耗时: {list_time:.6f} 秒")
    print(f"在集合中查找耗时: {set_time:.6f} 秒")
    if set_time > 0:
        print(f"集合查找快 {list_time / set_time:.0f} 倍")

performance_tips_demo()

def simple_calculator(a, b, operation):
    """简单计算器函数"""
    if operation == '+':
        return a + b
    elif operation == '-':
        return a - b
    elif operation == '*':
        return a * b
    elif operation == '/':
        if b == 0:
            raise ValueError("不能除以零")
        return a / b
    else:
        raise ValueError(f"不支持的操作: {operation}")

def test_calculator():
    """测试计算器函数"""
    test_cases = [
        (5, 3, '+', 8),
        (5, 3, '-', 2),
        (5, 3, '*', 15),
        (6, 3, '/', 2),
    ]
    
    print("运行计算器测试:")
    all_passed = True
    
    for a, b, op, expected in test_cases:
        try:
            result = simple_calculator(a, b, op)
            if result == expected:
                print(f"✓ {a} {op} {b} = {result} (通过)")
            else:
                print(f"✗ {a} {op} {b} = {result}, 期望 {expected} (失败)")
                all_passed = False
        except Exception as e:
            print(f"✗ {a} {op} {b} 抛出异常: {e} (失败)")
            all_passed = False
    
    # 测试异常情况
    try:
        simple_calculator(5, 0, '/')
        print("✗ 除零测试失败 - 应该抛出异常")
        all_passed = False
    except ValueError:
        print("✓ 除零测试通过 - 正确抛出异常")
    
    print(f"\n测试结果: {'全部通过' if all_passed else '有测试失败'}")

test_calculator()